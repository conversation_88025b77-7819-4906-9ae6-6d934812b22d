import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import EmailConfirmed from './pages/EmailConfirmed';
import ReduxDemo from './pages/ReduxDemo';
import Settings from './pages/Settings';
import Dashboard from './pages/Dashboard';
import Pentesting from './pages/Pentesting';
import PhishingDetection from './pages/PhishingDetection';
import MalwareDetection from './pages/MalwareDetection';
import Incidents from './pages/Incidents';
import OpenVASAdmin from './pages/OpenVASAdmin';
import MainLayout from './layouts/MainLayout';
import AuthLayout from './layouts/AuthLayout';
import { AuthProvider } from './contexts/AuthContext';
import { NavigationProvider } from './contexts/NavigationContext';
import { ModalProvider } from './contexts/ModalContext';
import SessionExpirationWarning from './components/SessionExpirationWarning';
import TokenExpirationHandler from './components/TokenExpirationHandler';



function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <SessionExpirationWarning />
        <TokenExpirationHandler />
        <NavigationProvider>
          <ModalProvider>
            <Routes>
            {/* Routes d'authentification (sans Header/Sidebar) */}
            <Route path="/auth" element={<AuthLayout />}>
              <Route path="login" element={<Login />} />
              <Route path="register" element={<Register />} />
              <Route path="forgot-password" element={<ForgotPassword />} />
              <Route path="reset-password" element={<ResetPassword />} />
              <Route path="email-confirmed" element={<EmailConfirmed />} />
            </Route>

            {/* Routes principales (avec Header/Sidebar) */}
            <Route path="/app" element={<MainLayout><Pentesting /></MainLayout>} />
            <Route path="/dashboard" element={<MainLayout><Dashboard /></MainLayout>} />
            <Route path="/pentesting" element={<MainLayout><Pentesting /></MainLayout>} />
            <Route path="/phishing" element={<MainLayout><PhishingDetection /></MainLayout>} />
            <Route path="/malware" element={<MainLayout><MalwareDetection /></MainLayout>} />
            <Route path="/incidents" element={<MainLayout><Incidents /></MainLayout>} />
            <Route path="/redux-demo" element={<MainLayout><ReduxDemo /></MainLayout>} />
            <Route path="/settings" element={<MainLayout><Settings /></MainLayout>} />
            <Route path="/openvas-admin" element={<MainLayout><OpenVASAdmin /></MainLayout>} />

            {/* Redirections */}
            <Route path="/login" element={<Navigate to="/auth/login" replace />} />
            <Route path="/register" element={<Navigate to="/auth/register" replace />} />
            <Route path="/" element={<Navigate to="/auth/login" replace />} />
            <Route path="*" element={<Navigate to="/auth/login" replace />} />
            </Routes>
          </ModalProvider>
        </NavigationProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;
