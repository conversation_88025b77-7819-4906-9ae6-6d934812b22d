import React from 'react';
import { Clock, AlertTriangle, TrendingUp, Activity, Zap } from 'lucide-react';

interface ProgressBarProps {
  progress: number;
  status: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

interface TimeIndicatorProps {
  createdAt: string;
  status: string;
  showDetails?: boolean;
}

interface UrgencyAlertProps {
  severity: string;
  timeElapsed: number; // in hours
  status: string;
}

interface StatusProgressProps {
  currentStatus: string;
  showSteps?: boolean;
}

// Progress bar component with animated fill
export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  status,
  showLabel = true,
  size = 'md'
}) => {
  const getBarHeight = () => {
    switch (size) {
      case 'sm': return 'h-1';
      case 'lg': return 'h-4';
      default: return 'h-2';
    }
  };

  const getProgressColor = () => {
    if (progress >= 90) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    if (progress >= 30) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="w-full">
      {showLabel && (
        <div className="flex justify-between text-xs text-gray-400 mb-1">
          <span>Progress</span>
          <span>{progress}%</span>
        </div>
      )}
      <div className={`w-full bg-gray-700 rounded-full ${getBarHeight()} overflow-hidden`}>
        <div 
          className={`${getBarHeight()} rounded-full transition-all duration-1000 ease-out ${getProgressColor()}`}
          style={{ 
            width: `${progress}%`,
            boxShadow: progress > 0 ? `0 0 10px ${getProgressColor().replace('bg-', 'rgba(').replace('-500', ', 0.5)')}` : 'none'
          }}
        >
          {size === 'lg' && (
            <div className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
          )}
        </div>
      </div>
    </div>
  );
};

// Time elapsed indicator with urgency colors
export const TimeIndicator: React.FC<TimeIndicatorProps> = ({
  createdAt,
  status,
  showDetails = true
}) => {
  const calculateTimeElapsed = () => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffMs = now.getTime() - created.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    return { hours: diffHours, days: diffDays };
  };

  const getUrgencyColor = (hours: number) => {
    const days = Math.floor(hours / 24);

    // Only use red for extremely overdue items (10+ days)
    if (days > 10) return 'text-red-400 bg-red-500/20 border-red-500/50 animate-pulse';
    if (days > 7) return 'text-orange-400 bg-orange-500/20 border-orange-500/50';
    if (days > 3) return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/50';
    if (hours > 24) return 'text-blue-400 bg-blue-500/20 border-blue-500/50';
    return 'text-green-400 bg-green-500/20 border-green-500/50';
  };

  const formatTimeElapsed = (hours: number, days: number) => {
    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h`;
    return 'Just now';
  };

  const { hours, days } = calculateTimeElapsed();
  const urgencyClass = getUrgencyColor(hours);

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-lg border text-xs font-medium ${urgencyClass}`}>
      <Clock size={12} className="mr-1" />
      <span>{formatTimeElapsed(hours, days)}</span>
      {showDetails && hours > 48 && (
        <AlertTriangle size={12} className="ml-1 animate-pulse" />
      )}
    </div>
  );
};

// Urgency alert for critical items
export const UrgencyAlert: React.FC<UrgencyAlertProps> = ({
  severity,
  timeElapsed,
  status
}) => {
  const daysSinceCreation = Math.floor(timeElapsed / 24);

  // Only show red blinking alert for items over 10 days old and still open
  const isExtremelyOverdue = daysSinceCreation > 10 && (status === 'open' || status === 'new' || status === 'in_progress');

  const shouldShowAlert = () => {
    // Show extreme overdue alert (red blinking)
    if (isExtremelyOverdue) return true;

    // Show moderate alerts for other cases (no blinking, different colors)
    if (severity === 'critical' && timeElapsed > 48) return true; // 2 days for critical
    if (severity === 'high' && timeElapsed > 72) return true; // 3 days for high
    if (daysSinceCreation > 7 && (status === 'open' || status === 'new')) return true; // 7 days for any open item

    return false;
  };

  const getAlertConfig = () => {
    if (isExtremelyOverdue) {
      return {
        bgColor: 'bg-red-600/30',
        borderColor: 'border-red-500/70',
        textColor: 'text-red-300',
        iconColor: 'text-red-400',
        animation: 'animate-pulse',
        iconAnimation: 'animate-bounce',
        message: `EXTREMELY OVERDUE: ${daysSinceCreation} days without resolution!`,
        priority: 'CRITICAL'
      };
    }

    if (severity === 'critical' && timeElapsed > 48) {
      return {
        bgColor: 'bg-orange-600/20',
        borderColor: 'border-orange-500/50',
        textColor: 'text-orange-300',
        iconColor: 'text-orange-400',
        animation: '',
        iconAnimation: '',
        message: 'Critical item requires attention',
        priority: 'HIGH'
      };
    }

    if (severity === 'high' && timeElapsed > 72) {
      return {
        bgColor: 'bg-yellow-600/20',
        borderColor: 'border-yellow-500/50',
        textColor: 'text-yellow-300',
        iconColor: 'text-yellow-400',
        animation: '',
        iconAnimation: '',
        message: 'High priority item overdue',
        priority: 'MEDIUM'
      };
    }

    // Default for items over 7 days
    return {
      bgColor: 'bg-blue-600/20',
      borderColor: 'border-blue-500/50',
      textColor: 'text-blue-300',
      iconColor: 'text-blue-400',
      animation: '',
      iconAnimation: '',
      message: 'Item requires follow-up',
      priority: 'LOW'
    };
  };

  if (!shouldShowAlert()) return null;

  const config = getAlertConfig();

  return (
    <div className={`${config.bgColor} border ${config.borderColor} rounded-lg p-3 mb-4 ${config.animation}`}>
      <div className="flex items-center">
        <AlertTriangle className={`${config.iconColor} mr-2 ${config.iconAnimation}`} size={18} />
        <div>
          <p className={`${config.textColor} font-semibold text-sm`}>{config.message}</p>
          <p className="text-gray-400 text-xs">
            {daysSinceCreation > 0 ? `${daysSinceCreation} days` : `${timeElapsed} hours`} since creation
          </p>
        </div>
      </div>
    </div>
  );
};

// Status progress stepper
export const StatusProgress: React.FC<StatusProgressProps> = ({
  currentStatus,
  showSteps = true
}) => {
  const statusSteps = [
    { key: 'new', label: 'New', icon: Zap },
    { key: 'open', label: 'Open', icon: Activity },
    { key: 'in_progress', label: 'In Progress', icon: TrendingUp },
    { key: 'resolved', label: 'Resolved', icon: Clock },
    { key: 'closed', label: 'Closed', icon: AlertTriangle }
  ];

  const getCurrentStepIndex = () => {
    return statusSteps.findIndex(step => step.key === currentStatus.toLowerCase().replace(' ', '_'));
  };

  const currentIndex = getCurrentStepIndex();

  if (!showSteps) {
    return (
      <div className="flex items-center">
        <div className={`w-3 h-3 rounded-full mr-2 ${
          currentIndex >= 0 ? 'bg-blue-500 animate-pulse' : 'bg-gray-500'
        }`}></div>
        <span className="text-sm text-gray-300">{currentStatus}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      {statusSteps.map((step, index) => {
        const Icon = step.icon;
        const isActive = index <= currentIndex;
        const isCurrent = index === currentIndex;
        
        return (
          <div key={step.key} className="flex items-center">
            <div className={`relative flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
              isActive 
                ? 'bg-blue-500 border-blue-500 text-white' 
                : 'bg-gray-700 border-gray-600 text-gray-400'
            } ${isCurrent ? 'animate-pulse shadow-lg shadow-blue-500/50' : ''}`}>
              <Icon size={14} />
              {isCurrent && (
                <div className="absolute inset-0 rounded-full bg-blue-500 animate-ping opacity-75"></div>
              )}
            </div>
            {index < statusSteps.length - 1 && (
              <div className={`w-8 h-0.5 transition-all duration-300 ${
                index < currentIndex ? 'bg-blue-500' : 'bg-gray-600'
              }`}></div>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Activity pulse indicator
interface ActivityPulseProps {
  isActive: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'yellow' | 'red';
}

export const ActivityPulse: React.FC<ActivityPulseProps> = ({
  isActive,
  size = 'md',
  color = 'blue'
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm': return 'w-2 h-2';
      case 'lg': return 'w-4 h-4';
      default: return 'w-3 h-3';
    }
  };

  const getColorClass = () => {
    switch (color) {
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'red': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  return (
    <div className="relative">
      <div className={`${getSizeClass()} ${getColorClass()} rounded-full ${
        isActive ? 'animate-pulse' : ''
      }`}></div>
      {isActive && (
        <div className={`absolute inset-0 ${getSizeClass()} ${getColorClass()} rounded-full animate-ping opacity-75`}></div>
      )}
    </div>
  );
};

// Combined dashboard widget
interface TicketMetricsProps {
  ticket: {
    status: string;
    severity?: string;
    created_at: string;
    priority?: number;
  };
}

export const TicketMetrics: React.FC<TicketMetricsProps> = ({ ticket }) => {
  const timeElapsed = Math.floor((new Date().getTime() - new Date(ticket.created_at).getTime()) / (1000 * 60 * 60));

  return (
    <div className="space-y-3">
      <UrgencyAlert
        severity={ticket.severity || 'medium'}
        timeElapsed={timeElapsed}
        status={ticket.status}
      />
      <div className="flex items-center justify-between">
        <TimeIndicator createdAt={ticket.created_at} status={ticket.status} />
        <ActivityPulse
          isActive={ticket.status === 'in_progress' || ticket.status === 'new'}
          color={ticket.severity === 'critical' ? 'red' : 'blue'}
        />
      </div>
    </div>
  );
};


