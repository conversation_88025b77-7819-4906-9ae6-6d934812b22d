import React, { useState } from 'react';
import { Search, Filter, X, Calendar, User, AlertTriangle, Clock } from 'lucide-react';

export interface FilterOptions {
  searchText: string;
  priority: string[];
  status: string[];
  type: string[];
  assignedTo: string[];
  dateRange: {
    start: string;
    end: string;
  };
}

interface FilterAndSearchProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  availableAssignees?: string[];
  availableTypes?: string[];
}

export const FilterAndSearch: React.FC<FilterAndSearchProps> = ({
  filters,
  onFiltersChange,
  availableAssignees = [],
  availableTypes = []
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const priorityOptions = [
    { value: 'critical', color: 'text-red-400' },
    { value: 'high', color: 'text-orange-400' },
    { value: 'medium', color: 'text-yellow-400' },
    { value: 'low',  color: 'text-green-400' }
  ];

  const statusOptions = [
    { value: 'new', label: 'Nouveau', color: 'text-sky-400' },
    { value: 'open', label: 'Ouvert', color: 'text-blue-400' },
    { value: 'in_progress', label: 'En cours', color: 'text-yellow-400' },
    { value: 'on_hold', label: 'En attente', color: 'text-amber-400' },
    { value: 'resolved', label: 'Résolu', color: 'text-emerald-400' },
    { value: 'closed', label: 'Fermé', color: 'text-gray-400' }
  ];

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const toggleArrayFilter = (key: 'priority' | 'status' | 'type' | 'assignedTo', value: string) => {
    const currentArray = filters[key];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    updateFilter(key, newArray);
  };

  const clearAllFilters = () => {
    onFiltersChange({
      searchText: '',
      priority: [],
      status: [],
      type: [],
      assignedTo: [],
      dateRange: { start: '', end: '' }
    });
  };

  const hasActiveFilters = () => {
    return filters.searchText ||
           filters.priority.length > 0 ||
           filters.status.length > 0 ||
           filters.type.length > 0 ||
           filters.assignedTo.length > 0 ||
           filters.dateRange.start ||
           filters.dateRange.end;
  };

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 mb-6">
      {/* Barre de recherche principale */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="flex-1 relative">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher par ID, titre, description..."
            value={filters.searchText}
            onChange={(e) => updateFilter('searchText', e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
          />
        </div>
        
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`flex items-center space-x-2 px-4 py-3 rounded-xl border transition-all duration-200 ${
            hasActiveFilters() 
              ? 'bg-blue-600/20 border-blue-500/50 text-blue-400' 
              : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500'
          }`}
        >
          <Filter size={20} />
          <span>Filtres</span>
          {hasActiveFilters() && (
            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
              {[filters.priority, filters.status, filters.type, filters.assignedTo].reduce((acc, arr) => acc + arr.length, 0)}
            </span>
          )}
        </button>

        {hasActiveFilters() && (
          <button
            onClick={clearAllFilters}
            className="flex items-center space-x-2 px-4 py-3 bg-red-600/20 border border-red-500/50 text-red-400 rounded-xl hover:bg-red-600/30 transition-all duration-200"
          >
            <X size={20} />
            <span>Effacer</span>
          </button>
        )}
      </div>

      {/* Filtres avancés */}
      {isExpanded && (
        <div className="space-y-6 pt-4 border-t border-gray-700/50">
          {/* Filtres par priorité */}
          <div>
            <h3 className="text-white font-semibold mb-3 flex items-center">
              <AlertTriangle size={16} className="mr-2" />
              Priorité
            </h3>
            <div className="flex flex-wrap gap-2">
              {priorityOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => toggleArrayFilter('priority', option.value)}
                  className={`px-3 py-2 rounded-lg border text-sm font-medium transition-all duration-200 ${
                    filters.priority.includes(option.value)
                      ? 'bg-blue-600/30 border-blue-500/50 text-blue-300'
                      : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500'
                  }`}
                >
                  {option.value.charAt(0).toUpperCase() + option.value.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Filtres par statut */}
          <div>
            <h3 className="text-white font-semibold mb-3 flex items-center">
              <Clock size={16} className="mr-2" />
              Statut
            </h3>
            <div className="flex flex-wrap gap-2">
              {statusOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => toggleArrayFilter('status', option.value)}
                  className={`px-3 py-2 rounded-lg border text-sm font-medium transition-all duration-200 ${
                    filters.status.includes(option.value)
                      ? 'bg-blue-600/30 border-blue-500/50 text-blue-300'
                      : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Filtres par type */}
          {availableTypes.length > 0 && (
            <div>
              <h3 className="text-white font-semibold mb-3">Type d'incident</h3>
              <div className="flex flex-wrap gap-2">
                {availableTypes.map((type) => (
                  <button
                    key={type}
                    onClick={() => toggleArrayFilter('type', type)}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium transition-all duration-200 ${
                      filters.type.includes(type)
                        ? 'bg-blue-600/30 border-blue-500/50 text-blue-300'
                        : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500'
                    }`}
                  >
                    {type}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Filtres par assigné */}
          {availableAssignees.length > 0 && (
            <div>
              <h3 className="text-white font-semibold mb-3 flex items-center">
                <User size={16} className="mr-2" />
                Assigné à
              </h3>
              <div className="flex flex-wrap gap-2">
                {availableAssignees.map((assignee) => (
                  <button
                    key={assignee}
                    onClick={() => toggleArrayFilter('assignedTo', assignee)}
                    className={`px-3 py-2 rounded-lg border text-sm font-medium transition-all duration-200 ${
                      filters.assignedTo.includes(assignee)
                        ? 'bg-blue-600/30 border-blue-500/50 text-blue-300'
                        : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:text-white hover:border-gray-500'
                    }`}
                  >
                    {assignee}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Filtres par date */}
          <div>
            <h3 className="text-white font-semibold mb-3 flex items-center">
              <Calendar size={16} className="mr-2" />
              Période de création
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-400 text-sm mb-2">Du</label>
                <input
                  type="date"
                  value={filters.dateRange.start}
                  onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, start: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                />
              </div>
              <div>
                <label className="block text-gray-400 text-sm mb-2">Au</label>
                <input
                  type="date"
                  value={filters.dateRange.end}
                  onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, end: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
