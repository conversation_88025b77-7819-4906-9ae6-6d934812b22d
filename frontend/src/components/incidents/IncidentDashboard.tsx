import React from 'react';
import { Al<PERSON><PERSON><PERSON>gle, Clock, CheckCircle, TrendingUp, Activity, Zap } from 'lucide-react';
import { InvestigationTicket, Incident } from '../../services/incidentService';
import { ActivityPulse } from './AdvancedIndicators';

interface IncidentDashboardProps {
  tickets: InvestigationTicket[];
  incidents: Incident[];
}

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  isActive?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color,
  trend,
  isActive = false
}) => {
  return (
    <div className={`bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-4 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl ${color}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <div className={`p-2 rounded-lg ${color.replace('border-', 'bg-').replace('/50', '/20')}`}>
            {icon}
          </div>
          <ActivityPulse isActive={isActive} size="sm" />
        </div>
        {trend && (
          <div className={`flex items-center text-xs ${trend.isPositive ? 'text-green-400' : 'text-red-400'}`}>
            <TrendingUp size={12} className={`mr-1 ${!trend.isPositive ? 'rotate-180' : ''}`} />
            {trend.value}%
          </div>
        )}
      </div>
      <div>
        <h3 className="text-xl font-bold text-white mb-1">{value}</h3>
        <p className="text-gray-400 text-xs">{title}</p>
      </div>
    </div>
  );
};

interface CompactDashboardProps {
  tickets: InvestigationTicket[];
  incidents: Incident[];
}

export const CompactDashboard: React.FC<CompactDashboardProps> = ({
  tickets,
  incidents
}) => {
  // Calculate statistics
  const stats = React.useMemo(() => {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Ticket statistics
    const openTickets = tickets.filter(t => t.status === 'open' || t.status === 'new').length;
    const inProgressTickets = tickets.filter(t => t.status === 'in_progress').length;
    const criticalTickets = tickets.filter(t =>
      (t.severity === 'critical' || t.priority === 1)
    ).length;

    // Incident statistics
    const openIncidents = incidents.filter(i => i.status === 'open').length;
    const criticalIncidents = incidents.filter(i => i.severity === 'critical').length;

    // Extremely overdue items (more than 10 days old and still open)
    const extremelyOverdueTickets = tickets.filter(t => {
      const created = new Date(t.created_at);
      const daysOld = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
      return daysOld > 10 && (t.status === 'open' || t.status === 'new' || t.status === 'in_progress');
    }).length;

    const extremelyOverdueIncidents = incidents.filter(i => {
      const created = new Date(i.created_at);
      const daysOld = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
      return daysOld > 10 && (i.status === 'open' || i.status === 'in_progress');
    }).length;

    return {
      openTickets,
      inProgressTickets,
      criticalTickets,
      openIncidents,
      criticalIncidents,
      extremelyOverdueTickets,
      extremelyOverdueIncidents,
      totalExtremelyOverdue: extremelyOverdueTickets + extremelyOverdueIncidents,
      totalCritical: criticalTickets + criticalIncidents
    };
  }, [tickets, incidents]);

  return (
    <div className="mb-6">
      {/* Critical Alert Banner - Only for extremely overdue items */}
      {stats.totalExtremelyOverdue > 0 && (
        <div className="bg-red-600/20 border border-red-500/50 rounded-lg p-3 mb-3 animate-pulse">
          <div className="flex items-center">
            <AlertTriangle className="text-red-400 mr-2 animate-bounce" size={16} />
            <span className="text-red-400 font-semibold text-xs">
              URGENT: {stats.totalExtremelyOverdue} items overdue for more than 10 days!
            </span>
          </div>
        </div>
      )}

      {/* Moderate Alert Banner - For critical items */}
      {stats.totalCritical > 0 && stats.totalExtremelyOverdue === 0 && (
        <div className="bg-orange-600/20 border border-orange-500/50 rounded-lg p-3 mb-3">
          <div className="flex items-center">
            <AlertTriangle className="text-orange-400 mr-2" size={16} />
            <span className="text-orange-400 font-semibold text-xs">
              {stats.totalCritical} critical items require attention
            </span>
          </div>
        </div>
      )}

      {/* Compact Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <StatCard
          title="Open Tickets"
          value={stats.openTickets}
          icon={<AlertTriangle size={20} className="text-blue-400" />}
          color="border-blue-500/50"
          isActive={stats.openTickets > 0}
        />

        <StatCard
          title="In Progress"
          value={stats.inProgressTickets}
          icon={<Activity size={20} className="text-yellow-400" />}
          color="border-yellow-500/50"
          isActive={stats.inProgressTickets > 0}
        />

        <StatCard
          title="Critical Items"
          value={stats.totalCritical}
          icon={<Zap size={20} className="text-red-400" />}
          color="border-red-500/50"
          isActive={stats.totalCritical > 0}
        />

        <StatCard
          title="Open Incidents"
          value={stats.openIncidents}
          icon={<CheckCircle size={20} className="text-purple-400" />}
          color="border-purple-500/50"
          isActive={stats.openIncidents > 0}
        />
      </div>
    </div>
  );
};

export const IncidentDashboard: React.FC<IncidentDashboardProps> = ({
  tickets,
  incidents
}) => {
  // Calculate statistics
  const stats = React.useMemo(() => {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Debug: Log raw data
    console.log('🔍 Dashboard Debug - Raw data:');
    console.log('Total tickets received:', tickets.length);
    console.log('Total incidents received:', incidents.length);
    console.log('Sample ticket statuses:', tickets.slice(0, 3).map(t => ({ id: t.id, status: t.status, severity: t.severity, priority: t.priority })));
    console.log('Sample incident statuses:', incidents.slice(0, 3).map(i => ({ id: i.id, status: i.status, severity: i.severity })));

    // Ticket statistics
    const totalTickets = tickets.length;

    // Fix: Check for all possible "open" status variations
    const openTickets = tickets.filter(t => {
      const status = t.status?.toLowerCase();
      return status === 'open' || status === 'new' || status === 'pending';
    }).length;

    const inProgressTickets = tickets.filter(t => {
      const status = t.status?.toLowerCase();
      return status === 'in_progress' || status === 'in progress' || status === 'assigned';
    }).length;

    // Fix: Check for critical tickets with multiple criteria
    const criticalTickets = tickets.filter(t => {
      const severity = t.severity?.toLowerCase();
      const impact = t.impact?.toLowerCase();
      const priority = t.priority;
      return severity === 'critical' || impact === 'critical' || priority === 1 || priority === '1';
    }).length;

    const recentTickets = tickets.filter(t => {
      const created = new Date(t.created_at);
      return created > last24Hours && !isNaN(created.getTime());
    }).length;

    // Incident statistics
    const totalIncidents = incidents.length;

    // Fix: Check for all possible "open" status variations
    const openIncidents = incidents.filter(i => {
      const status = i.status?.toLowerCase();
      return status === 'open' || status === 'new' || status === 'pending';
    }).length;

    // Fix: Check for critical incidents
    const criticalIncidents = incidents.filter(i => {
      const severity = i.severity?.toLowerCase();
      return severity === 'critical';
    }).length;

    const recentIncidents = incidents.filter(i => {
      const created = new Date(i.created_at);
      return created > last24Hours && !isNaN(created.getTime());
    }).length;
    
    // Extremely overdue items (more than 10 days old and still open)
    const extremelyOverdueTickets = tickets.filter(t => {
      const created = new Date(t.created_at);
      const daysOld = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
      return daysOld > 10 && (t.status === 'open' || t.status === 'new' || t.status === 'in_progress');
    }).length;

    const extremelyOverdueIncidents = incidents.filter(i => {
      const created = new Date(i.created_at);
      const daysOld = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
      return daysOld > 10 && (i.status === 'open' || i.status === 'in_progress');
    }).length;

    // Regular overdue items (more than 3 days old and still open)
    const overdueTickets = tickets.filter(t => {
      const created = new Date(t.created_at);
      const daysOld = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
      return daysOld > 3 && daysOld <= 10 && (t.status === 'open' || t.status === 'new' || t.status === 'in_progress');
    }).length;

    const overdueIncidents = incidents.filter(i => {
      const created = new Date(i.created_at);
      const daysOld = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
      return daysOld > 3 && daysOld <= 10 && (i.status === 'open' || i.status === 'in_progress');
    }).length;
    
    // Debug: Log calculated statistics
    const calculatedStats = {
      totalTickets,
      openTickets,
      inProgressTickets,
      criticalTickets,
      recentTickets,
      totalIncidents,
      openIncidents,
      criticalIncidents,
      recentIncidents,
      overdueTickets,
      overdueIncidents,
      extremelyOverdueTickets,
      extremelyOverdueIncidents,
      totalOverdue: overdueTickets + overdueIncidents,
      totalExtremelyOverdue: extremelyOverdueTickets + extremelyOverdueIncidents
    };

    console.log('📊 Dashboard Calculated Statistics:');
    console.log('Tickets - Total:', totalTickets, 'Open:', openTickets, 'In Progress:', inProgressTickets, 'Critical:', criticalTickets);
    console.log('Incidents - Total:', totalIncidents, 'Open:', openIncidents, 'Critical:', criticalIncidents);
    console.log('Overdue - Regular:', overdueTickets + overdueIncidents, 'Extremely:', extremelyOverdueTickets + extremelyOverdueIncidents);

    return calculatedStats;
  }, [tickets, incidents]);

  return (
    <div className="space-y-6">
      {/* Critical Alerts - Only for extremely overdue items */}
      {stats.totalExtremelyOverdue > 0 && (
        <div className="bg-red-600/20 border border-red-500/50 rounded-2xl p-6 animate-pulse">
          <div className="flex items-center mb-4">
            <AlertTriangle className="text-red-400 mr-3 animate-bounce" size={24} />
            <h3 className="text-red-400 font-semibold text-lg">URGENT: Items Overdue for 10+ Days!</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {stats.extremelyOverdueTickets > 0 && (
              <div className="text-red-300">
                <span className="font-semibold">{stats.extremelyOverdueTickets}</span> extremely overdue tickets
              </div>
            )}
            {stats.extremelyOverdueIncidents > 0 && (
              <div className="text-red-300">
                <span className="font-semibold">{stats.extremelyOverdueIncidents}</span> extremely overdue incidents
              </div>
            )}
          </div>
        </div>
      )}

      {/* Moderate Alerts - For critical items and regular overdue */}
      {(stats.criticalTickets > 0 || stats.criticalIncidents > 0 || stats.totalOverdue > 0) && stats.totalExtremelyOverdue === 0 && (
        <div className="bg-orange-600/20 border border-orange-500/50 rounded-2xl p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="text-orange-400 mr-3" size={24} />
            <h3 className="text-orange-400 font-semibold text-lg">Attention Required</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            {stats.criticalTickets > 0 && (
              <div className="text-orange-300">
                <span className="font-semibold">{stats.criticalTickets}</span> critical tickets
              </div>
            )}
            {stats.criticalIncidents > 0 && (
              <div className="text-orange-300">
                <span className="font-semibold">{stats.criticalIncidents}</span> critical incidents
              </div>
            )}
            {stats.totalOverdue > 0 && (
              <div className="text-orange-300">
                <span className="font-semibold">{stats.totalOverdue}</span> overdue items (3-10 days)
              </div>
            )}
          </div>
        </div>
      )}

      {/* Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <StatCard
          title="Open Tickets"
          value={stats.openTickets}
          icon={<AlertTriangle size={20} className="text-blue-400" />}
          color="border-blue-500/50"
          isActive={stats.openTickets > 0}
        />

        <StatCard
          title="In Progress"
          value={stats.inProgressTickets}
          icon={<Activity size={20} className="text-yellow-400" />}
          color="border-yellow-500/50"
          isActive={stats.inProgressTickets > 0}
        />

        <StatCard
          title="Critical Items"
          value={stats.criticalTickets + stats.criticalIncidents}
          icon={<Zap size={20} className="text-orange-400" />}
          color="border-orange-500/50"
          isActive={stats.criticalTickets + stats.criticalIncidents > 0}
        />

        <StatCard
          title="Extremely Overdue"
          value={stats.totalExtremelyOverdue}
          icon={<AlertTriangle size={20} className="text-red-400" />}
          color="border-red-500/50"
          isActive={stats.totalExtremelyOverdue > 0}
        />

        <StatCard
          title="Recent (24h)"
          value={stats.recentTickets + stats.recentIncidents}
          icon={<Clock size={20} className="text-green-400" />}
          color="border-green-500/50"
          isActive={stats.recentTickets + stats.recentIncidents > 0}
        />
      </div>

      {/* Detailed Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tickets Breakdown */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <h3 className="text-white font-semibold text-lg mb-4 flex items-center">
            <AlertTriangle size={20} className="mr-2 text-blue-400" />
            Investigation Tickets
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Total</span>
              <span className="text-white font-semibold">{stats.totalTickets}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Open/New</span>
              <span className="text-blue-400 font-semibold">{stats.openTickets}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">In Progress</span>
              <span className="text-yellow-400 font-semibold">{stats.inProgressTickets}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Critical</span>
              <span className="text-orange-400 font-semibold">{stats.criticalTickets}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Overdue (3-10d)</span>
              <span className="text-yellow-400 font-semibold">{stats.overdueTickets}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Extremely Overdue (10+d)</span>
              <span className="text-red-400 font-semibold animate-pulse">{stats.extremelyOverdueTickets}</span>
            </div>
          </div>
        </div>

        {/* Incidents Breakdown */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <h3 className="text-white font-semibold text-lg mb-4 flex items-center">
            <CheckCircle size={20} className="mr-2 text-purple-400" />
            Security Incidents
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Total</span>
              <span className="text-white font-semibold">{stats.totalIncidents}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Open</span>
              <span className="text-blue-400 font-semibold">{stats.openIncidents}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Critical</span>
              <span className="text-orange-400 font-semibold">{stats.criticalIncidents}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Recent (24h)</span>
              <span className="text-green-400 font-semibold">{stats.recentIncidents}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Overdue (3-10d)</span>
              <span className="text-yellow-400 font-semibold">{stats.overdueIncidents}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Extremely Overdue (10+d)</span>
              <span className="text-red-400 font-semibold animate-pulse">{stats.extremelyOverdueIncidents}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
