import React from 'react';
import { Eye, Edit, UserPlus, XCircle, Clock, User, AlertTriangle, Shield, RefreshCw } from 'lucide-react';
import { Incident } from '../../services/incidentService';
import { SeverityBadge, StatusBadge } from './InteractiveBadges';
import { formatDate } from '../../services/incidentService';

interface IncidentTableProps {
  incidents: Incident[];
  onView: (incident: Incident) => void;
  onEdit: (incident: Incident) => void;
  onAssign: (incident: Incident) => void;
  onClose?: (incident: Incident) => void;
  onRefresh?: () => void;
  canEdit: boolean;
  canAssign: boolean;
  canClose: boolean;
  isLoading?: boolean;
}

export const IncidentTable: React.FC<IncidentTableProps> = ({
  incidents,
  onView,
  onEdit,
  onAssign,
  onClose,
  onRefresh,
  canEdit,
  canAssign,
  canClose,
  isLoading = false
}) => {
  const getTimeElapsed = (createdAt: string) => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffHours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) return `${diffDays}j`;
    if (diffHours > 0) return `${diffHours}h`;
    return 'Nouveau';
  };

  const getUrgencyIndicator = (incident: Incident) => {
    const daysSinceCreation = Math.floor((new Date().getTime() - new Date(incident.created_at).getTime()) / (1000 * 60 * 60 * 24));
    const isExtremelyOverdue = daysSinceCreation > 10 && (incident.status === 'open' || incident.status === 'in_progress');
    const isCritical = incident.severity === 'critical';
    
    if (isExtremelyOverdue) {
      return <AlertTriangle size={16} className="text-red-500 animate-pulse" />;
    } else if (isCritical) {
      return <AlertTriangle size={16} className="text-orange-500" />;
    }
    return null;
  };

  const getIncidentTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'phishing':
        return '🎣';
      case 'malware':
        return '🦠';
      case 'vulnerability':
        return '🔓';
      case 'fraud':
        return '💳';
      case 'data_breach':
        return '📊';
      default:
        return '🛡️';
    }
  };

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl overflow-hidden">
      {/* En-tête avec bouton de rafraîchissement */}
      {onRefresh && (
        <div className="flex items-center justify-between p-4 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-white">
            Incidents ({incidents.length})
          </h3>
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
              isLoading
                ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                : 'bg-green-600/20 border border-green-500/50 text-green-400 hover:bg-green-600/30'
            }`}
            title="Actualiser les incidents"
          >
            <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
            <span className="text-sm">
              {isLoading ? 'Actualisation...' : 'Actualiser'}
            </span>
          </button>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-700/50 bg-gray-800/40">
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">🚨</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">ID Incident</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Statut</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Type</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Titre</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Créé</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Assigné à</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Sévérité</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Actions</th>
            </tr>
          </thead>
          <tbody>
            {incidents.map((incident) => (
              <tr 
                key={incident.id} 
                className="border-b border-gray-700/30 hover:bg-gray-700/20 transition-colors duration-200"
              >
                {/* Urgency Indicator */}
                <td className="p-4">
                  {getUrgencyIndicator(incident)}
                </td>
                
                {/* Incident ID */}
                <td className="p-4">
                  <span className="text-purple-400 font-mono text-sm font-semibold">
                    {incident.incident_number}
                  </span>
                </td>
                
                {/* Status */}
                <td className="p-4">
                  <StatusBadge status={incident.status} size="sm" />
                </td>
                
                {/* Type */}
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">
                      {getIncidentTypeIcon(incident.incident_type)}
                    </span>
                    <span className="text-gray-300 text-sm">
                      {incident.incident_type || 'Sécurité'}
                    </span>
                  </div>
                </td>
                
                {/* Title */}
                <td className="p-4 max-w-xs">
                  <div className="text-white text-sm font-medium truncate">
                    {incident.title || 'Sans titre'}
                  </div>
                  <div className="text-gray-400 text-xs mt-1 truncate">
                    {incident.description}
                  </div>
                </td>
                
                {/* Created Date */}
                <td className="p-4">
                  <div className="text-gray-300 text-sm">
                    {new Date(incident.created_at).toLocaleDateString('fr-FR')}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {getTimeElapsed(incident.created_at)}
                  </div>
                </td>
                
                {/* Assigned To */}
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    <User size={14} className="text-gray-400" />
                    <span className="text-gray-300 text-sm">
                      {incident.assigned_to || 'Non assigné'}
                    </span>
                  </div>
                </td>
                
                {/* Severity */}
                <td className="p-4">
                  <SeverityBadge severity={incident.severity} size="sm" />
                </td>
                
                {/* Actions */}
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onView(incident)}
                      className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-lg transition-colors duration-200"
                      title="Voir les détails"
                    >
                      <Eye size={16} />
                    </button>
                    
                    {canEdit && (
                      <button
                        onClick={() => onEdit(incident)}
                        className="p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/20 rounded-lg transition-colors duration-200"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                    )}
                    
                    {canAssign && (
                      <button
                        onClick={() => onAssign(incident)}
                        className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/20 rounded-lg transition-colors duration-200"
                        title="Assigner"
                      >
                        <UserPlus size={16} />
                      </button>
                    )}
                    
                    {canClose && onClose && (
                      <button
                        onClick={() => onClose(incident)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-colors duration-200"
                        title="Clôturer"
                      >
                        <XCircle size={16} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {incidents.length === 0 && (
        <div className="p-8 text-center text-gray-400">
          <Shield size={48} className="mx-auto mb-4 opacity-50" />
          <p className="text-lg">Aucun incident trouvé</p>
          <p className="text-sm">Créez un nouveau incident ou ajustez vos filtres</p>
        </div>
      )}
    </div>
  );
};
