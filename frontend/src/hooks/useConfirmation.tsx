import React, { useState, useCallback } from 'react';
import ConfirmationModal from '../components/common/ConfirmationModal';

interface ConfirmationOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
}

interface ConfirmationState extends ConfirmationOptions {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  loading: boolean;
}

export const useConfirmation = () => {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    title: '',
    message: '',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    type: 'warning',
    onConfirm: () => {},
    onCancel: () => {},
    loading: false
  });

  const confirm = useCallback((options: ConfirmationOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setState({
        isOpen: true,
        title: options.title,
        message: options.message,
        confirmText: options.confirmText || 'Confirm',
        cancelText: options.cancelText || 'Cancel',
        type: options.type || 'warning',
        loading: false,
        onConfirm: () => {
          setState(prev => ({ ...prev, loading: true }));
          setTimeout(() => {
            setState(prev => ({ ...prev, isOpen: false, loading: false }));
            resolve(true);
          }, 100);
        },
        onCancel: () => {
          setState(prev => ({ ...prev, isOpen: false }));
          resolve(false);
        }
      });
    });
  }, []);

  const ConfirmationComponent = useCallback(() => (
    <ConfirmationModal
      isOpen={state.isOpen}
      onClose={state.onCancel}
      onConfirm={state.onConfirm}
      title={state.title}
      message={state.message}
      confirmText={state.confirmText}
      cancelText={state.cancelText}
      type={state.type}
      loading={state.loading}
    />
  ), [state]);

  return {
    confirm,
    ConfirmationComponent
  };
};

export default useConfirmation;
